#!/usr/bin/env python3
"""
Grid Bot Adventure MCP Server Launcher

This launcher ensures proper Unicode handling on Windows systems.
"""

import sys
import os

def main():
    # Set UTF-8 encoding for Windows
    if sys.platform == "win32":
        os.environ["PYTHONIOENCODING"] = "utf-8"
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
    
    # Import and run the server
    try:
        from grid_game_server_minimal import app
        print("Grid Bot Adventure MCP Server starting (minimal version)...")
        app.run(transport="stdio")
    except ImportError as e:
        print(f"Error importing server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
